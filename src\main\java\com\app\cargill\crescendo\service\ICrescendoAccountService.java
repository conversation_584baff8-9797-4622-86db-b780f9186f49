package com.app.cargill.crescendo.service;

import java.util.Locale;

import org.springframework.context.support.ResourceBundleMessageSource;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.fasterxml.jackson.core.JsonProcessingException;

public interface ICrescendoAccountService {
	
	String createAccount(AccountDocument accountDocument,Locale locale, ResourceBundleMessageSource source) throws CustomDEExceptions, JsonProcessingException;

	String updateAccount(AccountDocument accountDocument, Locale locale,
			ResourceBundleMessageSource bundleMessageSource) throws CustomDEExceptions;
	

}
