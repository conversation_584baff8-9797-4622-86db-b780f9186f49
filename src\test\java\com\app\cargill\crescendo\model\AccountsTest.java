package com.app.cargill.crescendo.model;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AccountsTest {

  private Accounts accounts;
  private ObjectMapper objectMapper;

  @BeforeEach
  void setUp() {
    accounts = new Accounts();
    objectMapper = new ObjectMapper();
  }

  @Test
  void testDefaultConstructor() {
    Accounts newAccounts = new Accounts();
    assertNotNull(newAccounts);
    assertNull(newAccounts.getName());
    assertNull(newAccounts.getType());
    assertFalse(newAccounts.isActive());
    assertFalse(newAccounts.isMobileFirst());
    assertFalse(newAccounts.isProspectValidated());
    assertFalse(newAccounts.getAutoValidate());
    assertFalse(newAccounts.getIsDuplicate());
  }

  @Test
  void testAllArgsConstructor() {
    Accounts newAccounts = new Accounts(
        "Test Account",
        "Customer",
        "recordTypeId123",
        "123 Main St",
        "Test City",
        "Test State",
        "12345",
        "USA",
        "ownerId123",
        true,
        "Business Unit",
        "Business",
        true,
        true,
        "externalId123",
        "Golden Record",
        "Segment1",
        "9Box1",
        true,
        false,
        "Source System",
        "Account Type",
        "CUST001"
    );

    assertEquals("Test Account", newAccounts.getName());
    assertEquals("Customer", newAccounts.getType());
    assertEquals("recordTypeId123", newAccounts.getRecordTypeId());
    assertEquals("123 Main St", newAccounts.getBillingStreet());
    assertEquals("Test City", newAccounts.getBillingCity());
    assertEquals("Test State", newAccounts.getBillingState());
    assertEquals("12345", newAccounts.getBillingPostalCode());
    assertEquals("USA", newAccounts.getBillingCountry());
    assertEquals("ownerId123", newAccounts.getOwnerId());
    assertTrue(newAccounts.isActive());
    assertEquals("Business Unit", newAccounts.getBusinessUnit());
    assertEquals("Business", newAccounts.getBusiness());
    assertTrue(newAccounts.isMobileFirst());
    assertTrue(newAccounts.isProspectValidated());
    assertEquals("externalId123", newAccounts.getExternalId());
    assertEquals("Golden Record", newAccounts.getGoldenRecordName());
    assertEquals("Segment1", newAccounts.getSegmentStep1());
    assertEquals("9Box1", newAccounts.getX9BoxStep2());
    assertTrue(newAccounts.getAutoValidate());
    assertFalse(newAccounts.getIsDuplicate());
    assertEquals("Source System", newAccounts.getSourceSystem());
    assertEquals("Account Type", newAccounts.getAccountType());
    assertEquals("CUST001", newAccounts.getCustomerCode());
  }

  @Test
  void testSettersAndGetters() {
    accounts.setName("Test Account");
    accounts.setType("Customer");
    accounts.setRecordTypeId("recordTypeId123");
    accounts.setBillingStreet("123 Main St");
    accounts.setBillingCity("Test City");
    accounts.setBillingState("Test State");
    accounts.setBillingPostalCode("12345");
    accounts.setBillingCountry("USA");
    accounts.setOwnerId("ownerId123");
    accounts.setActive(true);
    accounts.setBusinessUnit("Business Unit");
    accounts.setBusiness("Business");
    accounts.setMobileFirst(true);
    accounts.setProspectValidated(true);
    accounts.setExternalId("externalId123");
    accounts.setGoldenRecordName("Golden Record");
    accounts.setSegmentStep1("Segment1");
    accounts.setX9BoxStep2("9Box1");
    accounts.setAutoValidate(true);
    accounts.setIsDuplicate(false);
    accounts.setSourceSystem("Source System");
    accounts.setAccountType("Account Type");
    accounts.setCustomerCode("CUST001");

    assertEquals("Test Account", accounts.getName());
    assertEquals("Customer", accounts.getType());
    assertEquals("recordTypeId123", accounts.getRecordTypeId());
    assertEquals("123 Main St", accounts.getBillingStreet());
    assertEquals("Test City", accounts.getBillingCity());
    assertEquals("Test State", accounts.getBillingState());
    assertEquals("12345", accounts.getBillingPostalCode());
    assertEquals("USA", accounts.getBillingCountry());
    assertEquals("ownerId123", accounts.getOwnerId());
    assertTrue(accounts.isActive());
    assertEquals("Business Unit", accounts.getBusinessUnit());
    assertEquals("Business", accounts.getBusiness());
    assertTrue(accounts.isMobileFirst());
    assertTrue(accounts.isProspectValidated());
    assertEquals("externalId123", accounts.getExternalId());
    assertEquals("Golden Record", accounts.getGoldenRecordName());
    assertEquals("Segment1", accounts.getSegmentStep1());
    assertEquals("9Box1", accounts.getX9BoxStep2());
    assertTrue(accounts.getAutoValidate());
    assertFalse(accounts.getIsDuplicate());
    assertEquals("Source System", accounts.getSourceSystem());
    assertEquals("Account Type", accounts.getAccountType());
    assertEquals("CUST001", accounts.getCustomerCode());
  }

  @Test
  void testJsonSerialization() throws JsonProcessingException {
    accounts.setName("Test Account");
    accounts.setType("Customer");
    accounts.setActive(true);
    accounts.setMobileFirst(true);
    accounts.setAutoValidate(true);
    accounts.setIsDuplicate(false);

    String json = objectMapper.writeValueAsString(accounts);
    
    assertNotNull(json);
    assertTrue(json.contains("\"Name\":\"Test Account\""));
    assertTrue(json.contains("\"Type\":\"Customer\""));
    assertTrue(json.contains("\"Active__c\":true"));
    assertTrue(json.contains("\"Mobile_First__c\":true"));
    assertTrue(json.contains("\"Auto_Validate__c\":true"));
    assertTrue(json.contains("\"Is_Duplicate__c\":false"));
  }

  @Test
  void testJsonDeserialization() throws JsonProcessingException {
    String json = """
        {
          "Name": "Test Account",
          "Type": "Customer",
          "RecordTypeId": "recordTypeId123",
          "BillingStreet": "123 Main St",
          "BillingCity": "Test City",
          "BillingState": "Test State",
          "BillingPostalCode": "12345",
          "BillingCountry": "USA",
          "OwnerId": "ownerId123",
          "Active__c": true,
          "Business_Uni__c": "Business Unit",
          "Business__c": "Business",
          "Mobile_First__c": true,
          "Prospect_Validated__c": true,
          "External_Id__c": "externalId123",
          "Golden_RecordName__c": "Golden Record",
          "Segment_Step_1__c": "Segment1",
          "X9_Box_Step_2__c": "9Box1",
          "Auto_Validate__c": true,
          "Is_Duplicate__c": false,
          "Source_System__c": "Source System",
          "Account_Type__c": "Account Type",
          "Customer_Code__c": "CUST001"
        }
        """;

    Accounts deserializedAccounts = objectMapper.readValue(json, Accounts.class);

    assertNotNull(deserializedAccounts);
    assertEquals("Test Account", deserializedAccounts.getName());
    assertEquals("Customer", deserializedAccounts.getType());
    assertEquals("recordTypeId123", deserializedAccounts.getRecordTypeId());
    assertEquals("123 Main St", deserializedAccounts.getBillingStreet());
    assertEquals("Test City", deserializedAccounts.getBillingCity());
    assertEquals("Test State", deserializedAccounts.getBillingState());
    assertEquals("12345", deserializedAccounts.getBillingPostalCode());
    assertEquals("USA", deserializedAccounts.getBillingCountry());
    assertEquals("ownerId123", deserializedAccounts.getOwnerId());
    assertTrue(deserializedAccounts.isActive());
    assertEquals("Business Unit", deserializedAccounts.getBusinessUnit());
    assertEquals("Business", deserializedAccounts.getBusiness());
    assertTrue(deserializedAccounts.isMobileFirst());
    assertTrue(deserializedAccounts.isProspectValidated());
    assertEquals("externalId123", deserializedAccounts.getExternalId());
    assertEquals("Golden Record", deserializedAccounts.getGoldenRecordName());
    assertEquals("Segment1", deserializedAccounts.getSegmentStep1());
    assertEquals("9Box1", deserializedAccounts.getX9BoxStep2());
    assertTrue(deserializedAccounts.getAutoValidate());
    assertFalse(deserializedAccounts.getIsDuplicate());
    assertEquals("Source System", deserializedAccounts.getSourceSystem());
    assertEquals("Account Type", deserializedAccounts.getAccountType());
    assertEquals("CUST001", deserializedAccounts.getCustomerCode());
  }

  @Test
  void testJsonIgnoreUnknownProperties() throws JsonProcessingException {
    String jsonWithUnknownProperty = """
        {
          "Name": "Test Account",
          "Type": "Customer",
          "UnknownProperty": "This should be ignored",
          "Active__c": true
        }
        """;

    assertDoesNotThrow(() -> {
      Accounts deserializedAccounts = objectMapper.readValue(jsonWithUnknownProperty, Accounts.class);
      assertEquals("Test Account", deserializedAccounts.getName());
      assertEquals("Customer", deserializedAccounts.getType());
      assertTrue(deserializedAccounts.isActive());
    });
  }

  @Test
  void testToString() {
    accounts.setName("Test Account");
    accounts.setType("Customer");
    accounts.setActive(true);

    String toString = accounts.toString();
    assertNotNull(toString);
    assertTrue(toString.contains("Test Account"));
    assertTrue(toString.contains("Customer"));
    assertTrue(toString.contains("true"));
  }

  @Test
  void testSerializable() {
    assertNotNull(Accounts.class.getInterfaces());
    boolean implementsSerializable = false;
    for (Class<?> interfaceClass : Accounts.class.getInterfaces()) {
      if (interfaceClass.equals(java.io.Serializable.class)) {
        implementsSerializable = true;
        break;
      }
    }
    assertTrue(implementsSerializable, "Accounts class should implement Serializable");
  }

  @Test
  void testDefaultBooleanValues() {
    Accounts newAccounts = new Accounts();
    
    // Test default values for Boolean fields
    assertEquals(false, newAccounts.getAutoValidate());
    assertEquals(false, newAccounts.getIsDuplicate());
    assertFalse(newAccounts.isActive());
    assertFalse(newAccounts.isMobileFirst());
    assertFalse(newAccounts.isProspectValidated());
  }

  @Test
  void testNullHandling() {
    accounts.setName(null);
    accounts.setType(null);
    accounts.setAutoValidate(null);
    accounts.setIsDuplicate(null);

    assertNull(accounts.getName());
    assertNull(accounts.getType());
    assertNull(accounts.getAutoValidate());
    assertNull(accounts.getIsDuplicate());
  }
}
