/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.CrescendoMetadata;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import java.io.Serializable;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

@ExtendWith(MockitoExtension.class)
class CrescendoApiServiceImplTest {

  @Mock private CrescendoApiReactiveService crescendoApiReactiveService;

  @InjectMocks private CrescendoApiServiceImpl crescendoApiServiceImpl;

  private AuthToken mockAuthToken;
  private VersionObject mockVersionObject;
  private CrescendoMetadata mockMetadata;

  @BeforeEach
  void setUp() {
    mockAuthToken = new AuthToken();
    mockAuthToken.setAccessToken("test-access-token");
    mockAuthToken.setInstanceUrl("https://test.salesforce.com");
    mockAuthToken.setIssuedAt(System.currentTimeMillis());

    mockVersionObject =
        VersionObject.builder()
            .version("58.0")
            .url("/services/data/v58.0")
            .label("Winter '24")
            .build();

    mockMetadata =
        CrescendoMetadata.builder()
            .authToken(mockAuthToken)
            .apiPath("/services/data/v58.0")
            .build();
  }

  @Test
  void getAuthToken_Success() {
    // Arrange
    when(crescendoApiReactiveService.getToken()).thenReturn(Mono.just(mockAuthToken));

    // Act
    AuthToken result = crescendoApiServiceImpl.getAuthToken();

    // Assert
    assertNotNull(result);
    assertEquals(mockAuthToken.getAccessToken(), result.getAccessToken());
    assertEquals(mockAuthToken.getInstanceUrl(), result.getInstanceUrl());
    verify(crescendoApiReactiveService).getToken();
  }

  @Test
  void getAuthToken_WebClientResponseException() {
    // Arrange
    WebClientResponseException exception =
        WebClientResponseException.create(401, "Unauthorized", null, new byte[0], null);
    when(crescendoApiReactiveService.getToken()).thenReturn(Mono.error(exception));

    // Act & Assert
    assertThrows(
        WebClientResponseException.class,
        () -> {
          crescendoApiServiceImpl.getAuthToken();
        });

    verify(crescendoApiReactiveService).getToken();
  }

  @Test
  void getLatestApiVersion_Success() {
    // Arrange
    when(crescendoApiReactiveService.getLatestApiVersion(mockAuthToken))
        .thenReturn(Mono.just(mockVersionObject));

    // Act
    VersionObject result = crescendoApiServiceImpl.getLatestApiVersion(mockAuthToken);

    // Assert
    assertNotNull(result);
    assertEquals(mockVersionObject.getVersion(), result.getVersion());
    assertEquals(mockVersionObject.getUrl(), result.getUrl());
    verify(crescendoApiReactiveService).getLatestApiVersion(mockAuthToken);
  }

  @Test
  void getLatestApiPath_Success() {
    // Arrange
    when(crescendoApiReactiveService.getLatestApiVersion(mockAuthToken))
        .thenReturn(Mono.just(mockVersionObject));

    // Act
    String result = crescendoApiServiceImpl.getLatestApiPath(mockAuthToken);

    // Assert
    assertEquals(mockVersionObject.getUrl(), result);
    verify(crescendoApiReactiveService).getLatestApiVersion(mockAuthToken);
  }

  @Test
  void init_Success() {
    // Arrange
    when(crescendoApiReactiveService.getToken()).thenReturn(Mono.just(mockAuthToken));
    when(crescendoApiReactiveService.getLatestApiVersion(mockAuthToken))
        .thenReturn(Mono.just(mockVersionObject));

    // Act
    CrescendoMetadata result = crescendoApiServiceImpl.init();

    // Assert
    assertNotNull(result);
    assertEquals(mockAuthToken, result.getAuthToken());
    assertEquals(mockVersionObject.getUrl(), result.getApiPath());
    verify(crescendoApiReactiveService).getToken();
    verify(crescendoApiReactiveService).getLatestApiVersion(mockAuthToken);
  }

  @Test
  void getTokenAndApiPath_WithValidMetadata() {
    // Arrange
    CrescendoApiServiceImpl.setCrescendoMetaData(mockMetadata);

    // Act
    AccessTokenAndApiPathDto result = crescendoApiServiceImpl.getTokenAndApiPath();

    // Assert
    assertNotNull(result);
    assertEquals(mockAuthToken, result.getAuthToken());
    assertEquals("/services/data/v58.0", result.getApiPath());
  }

  @Test
  void getTokenAndApiPath_WithNullMetadata() {
    // Arrange
    CrescendoApiServiceImpl.setCrescendoMetaData(null);
    when(crescendoApiReactiveService.getToken()).thenReturn(Mono.just(mockAuthToken));
    when(crescendoApiReactiveService.getLatestApiVersion(mockAuthToken))
        .thenReturn(Mono.just(mockVersionObject));

    // Act
    AccessTokenAndApiPathDto result = crescendoApiServiceImpl.getTokenAndApiPath();

    // Assert
    assertNotNull(result);
    assertEquals(mockAuthToken, result.getAuthToken());
    assertEquals(mockVersionObject.getUrl(), result.getApiPath());
    verify(crescendoApiReactiveService).getToken();
    verify(crescendoApiReactiveService).getLatestApiVersion(mockAuthToken);
  }

  @Test
  void getTokenAndApiPath_WithExpiredToken() {
    // Arrange
    AuthToken expiredToken = new AuthToken();
    expiredToken.setAccessToken("expired-token");
    expiredToken.setInstanceUrl("https://test.salesforce.com");
    expiredToken.setIssuedAt(System.currentTimeMillis() - (3 * 60 * 60 * 1000L)); // 3 hours ago

    CrescendoMetadata expiredMetadata =
        CrescendoMetadata.builder().authToken(expiredToken).apiPath("/services/data/v58.0").build();

    CrescendoApiServiceImpl.setCrescendoMetaData(expiredMetadata);
    when(crescendoApiReactiveService.getToken()).thenReturn(Mono.just(mockAuthToken));
    when(crescendoApiReactiveService.getLatestApiVersion(mockAuthToken))
        .thenReturn(Mono.just(mockVersionObject));

    // Act
    AccessTokenAndApiPathDto result = crescendoApiServiceImpl.getTokenAndApiPath();

    // Assert
    assertNotNull(result);
    assertEquals(mockAuthToken, result.getAuthToken());
    assertEquals(mockVersionObject.getUrl(), result.getApiPath());
    verify(crescendoApiReactiveService).getToken();
    verify(crescendoApiReactiveService).getLatestApiVersion(mockAuthToken);
  }

  @Test
  void getRecordsQuery_Success() {
    // Arrange
    String query = "SELECT Id, Name FROM User";
    ParameterizedTypeReference<SalesforceRecordsResponse<TestRecord>> typeReference =
        new ParameterizedTypeReference<>() {};

    TestRecord testRecord = new TestRecord("123", "Test User");
    SalesforceRecordsResponse<TestRecord> expectedResponse = new SalesforceRecordsResponse<>();
    expectedResponse.setTotalSize(1);
    expectedResponse.setDone(true);
    expectedResponse.setRecords(Arrays.asList(testRecord));

    when(crescendoApiReactiveService.getRecordsQuery(
            eq(mockAuthToken), eq("/services/data/v58.0"), eq(query), eq(typeReference)))
        .thenReturn(Mono.just(expectedResponse));

    // Act
    SalesforceRecordsResponse<TestRecord> result =
        crescendoApiServiceImpl.getRecordsQuery(
            mockAuthToken, "/services/data/v58.0", query, typeReference);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getTotalSize());
    assertTrue(result.isDone());
    assertEquals(1, result.getRecords().size());
    assertEquals("123", result.getRecords().get(0).getId());
    verify(crescendoApiReactiveService)
        .getRecordsQuery(mockAuthToken, "/services/data/v58.0", query, typeReference);
  }

  @Test
  void getRecordsQuery_NullResponse() {
    // Arrange
    String query = "SELECT Id, Name FROM User";
    ParameterizedTypeReference<SalesforceRecordsResponse<TestRecord>> typeReference =
        new ParameterizedTypeReference<>() {};

    when(crescendoApiReactiveService.getRecordsQuery(
            eq(mockAuthToken), eq("/services/data/v58.0"), eq(query), eq(typeReference)))
        .thenReturn(Mono.empty());

    // Act
    SalesforceRecordsResponse<TestRecord> result =
        crescendoApiServiceImpl.getRecordsQuery(
            mockAuthToken, "/services/data/v58.0", query, typeReference);

    // Assert
    assertNull(result);
    verify(crescendoApiReactiveService)
        .getRecordsQuery(mockAuthToken, "/services/data/v58.0", query, typeReference);
  }

  @Test
  void getCrescendoApiReactiveService_Success() {
    // Act
    CrescendoApiReactiveService result = crescendoApiServiceImpl.getCrescendoApiReactiveService();

    // Assert
    assertNotNull(result);
    assertEquals(crescendoApiReactiveService, result);
  }

  // Test record class for testing purposes
  private static class TestRecord implements Serializable {
    private String id;
    private String name;

    public TestRecord(String id, String name) {
      this.id = id;
      this.name = name;
    }

    public String getId() {
      return id;
    }

    public String getName() {
      return name;
    }
  }
}
