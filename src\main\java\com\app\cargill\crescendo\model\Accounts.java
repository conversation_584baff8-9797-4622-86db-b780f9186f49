package com.app.cargill.crescendo.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Accounts implements Serializable {
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@JsonProperty("Name")
    private String name;

    @JsonProperty("Type")
    private String type;

    @JsonProperty("RecordTypeId")
    private String recordTypeId;

    @JsonProperty("BillingStreet")
    private String billingStreet;

    @JsonProperty("BillingCity")
    private String billingCity;

    @JsonProperty("BillingState")
    private String billingState;

    @JsonProperty("BillingPostalCode")
    private String billingPostalCode;

    @JsonProperty("BillingCountry")
    private String billingCountry;

    @JsonProperty("OwnerId")
    private String ownerId;

    @JsonProperty("Active__c")
    private boolean active;

    @JsonProperty("Business_Uni__c")
    private String businessUnit;

    @JsonProperty("Business__c")
    private String business;

    @JsonProperty("Mobile_First__c")
    private boolean mobileFirst;

    @JsonProperty("Prospect_Validated__c")
    private boolean prospectValidated;

    @JsonProperty("External_Id__c")
    private String externalId;

    @JsonProperty("Golden_RecordName__c")
    private String goldenRecordName;

    @JsonProperty("Segment_Step_1__c")
    private String segmentStep1;

    @JsonProperty("X9_Box_Step_2__c")
    private String x9BoxStep2;

    @JsonProperty("Auto_Validate__c")
    private Boolean autoValidate = false;

    @JsonProperty("Is_Duplicate__c")
    private Boolean isDuplicate = false;

    @JsonProperty("Source_System__c")
    private String sourceSystem;

    @JsonProperty("Account_Type__c")
    private String accountType;

    @JsonProperty("Customer_Code__c")
    private String customerCode;

}
