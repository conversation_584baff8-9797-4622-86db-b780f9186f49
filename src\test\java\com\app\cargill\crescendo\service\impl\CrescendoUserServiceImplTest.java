/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.Users;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.ParameterizedTypeReference;

@ExtendWith(MockitoExtension.class)
class CrescendoUserServiceImplTest {

  @Mock private CrescendoApiServiceImpl crescendoApiServiceImpl;

  @InjectMocks private CrescendoUserServiceImpl crescendoUserServiceImpl;

  private AuthToken mockAuthToken;
  private AccessTokenAndApiPathDto mockTokenDto;
  private Users mockUser;
  private SalesforceRecordsResponse<Users> mockResponse;

  @BeforeEach
  void setUp() {
    mockAuthToken = new AuthToken();
    mockAuthToken.setAccessToken("test-access-token");
    mockAuthToken.setInstanceUrl("https://test.salesforce.com");
    mockAuthToken.setIssuedAt(System.currentTimeMillis());

    mockTokenDto =
        AccessTokenAndApiPathDto.builder()
            .authToken(mockAuthToken)
            .apiPath("/services/data/v58.0")
            .build();

    mockUser = new Users();
    mockUser.setId("003XX000004TmiQQAS");
    mockUser.setName("Test User");
    mockUser.setEmail("<EMAIL>");
    mockUser.setUsername("<EMAIL>");
    mockUser.setIsActive(true);

    mockResponse = new SalesforceRecordsResponse<>();
    mockResponse.setTotalSize(1);
    mockResponse.setDone(true);
    mockResponse.setRecords(Arrays.asList(mockUser));
  }

  @Test
  void getByUserEmail_Success() throws CustomDEExceptions {
    // Arrange
    String email = "<EMAIL>";
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(mockResponse);

    // Act
    SalesforceRecordsResponse<Users> result = crescendoUserServiceImpl.getByUserEmail(email);

    // Assert
    assertNotNull(result);
    assertEquals(1, result.getTotalSize());
    assertEquals(1, result.getRecords().size());
    assertEquals(email, result.getRecords().get(0).getEmail());
    assertTrue(result.getRecords().get(0).getIsActive());

    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class));
  }

  @Test
  void getByUserEmail_UserNotFound_NullResponse() {
    // Arrange
    String email = "<EMAIL>";
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(null);

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> {
              crescendoUserServiceImpl.getByUserEmail(email);
            });

    assertEquals("User not found on Crescendo", exception.getMessage());
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class));
  }

  @Test
  void getByUserEmail_UserNotFound_EmptyRecords() {
    // Arrange
    String email = "<EMAIL>";
    SalesforceRecordsResponse<Users> emptyResponse = new SalesforceRecordsResponse<>();
    emptyResponse.setTotalSize(0);
    emptyResponse.setDone(true);
    emptyResponse.setRecords(Collections.emptyList());

    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(emptyResponse);

    // Act & Assert - The implementation tries to access get(0) on empty list, causing
    // IndexOutOfBoundsException
    IndexOutOfBoundsException exception =
        assertThrows(
            IndexOutOfBoundsException.class,
            () -> {
              crescendoUserServiceImpl.getByUserEmail(email);
            });

    assertTrue(exception.getMessage().contains("Index: 0"));
  }

  @Test
  void getByUserEmail_UserNotFound_NullFirstRecord() {
    // Arrange
    String email = "<EMAIL>";
    SalesforceRecordsResponse<Users> responseWithNullRecord = new SalesforceRecordsResponse<>();
    responseWithNullRecord.setTotalSize(1);
    responseWithNullRecord.setDone(true);
    responseWithNullRecord.setRecords(Arrays.asList((Users) null));

    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(responseWithNullRecord);

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> {
              crescendoUserServiceImpl.getByUserEmail(email);
            });

    assertEquals("User not found on Crescendo", exception.getMessage());
  }

  @Test
  void getByUserEmail_MultipleUsersFound() {
    // Arrange
    String email = "<EMAIL>";
    Users secondUser = new Users();
    secondUser.setId("003XX000004TmiQQAT");
    secondUser.setName("Second User");
    secondUser.setEmail(email);
    secondUser.setIsActive(true);

    SalesforceRecordsResponse<Users> multipleUsersResponse = new SalesforceRecordsResponse<>();
    multipleUsersResponse.setTotalSize(2);
    multipleUsersResponse.setDone(true);
    multipleUsersResponse.setRecords(Arrays.asList(mockUser, secondUser));

    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(multipleUsersResponse);

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> {
              crescendoUserServiceImpl.getByUserEmail(email);
            });

    assertEquals("This email has more than one registration on Crecendo", exception.getMessage());
  }

  @Test
  void getByUserEmail_InactiveUser() {
    // Arrange
    String email = "<EMAIL>";
    Users inactiveUser = new Users();
    inactiveUser.setId("003XX000004TmiQQAS");
    inactiveUser.setName("Inactive User");
    inactiveUser.setEmail(email);
    inactiveUser.setIsActive(false);

    SalesforceRecordsResponse<Users> inactiveUserResponse = new SalesforceRecordsResponse<>();
    inactiveUserResponse.setTotalSize(1);
    inactiveUserResponse.setDone(true);
    inactiveUserResponse.setRecords(Arrays.asList(inactiveUser));

    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(inactiveUserResponse);

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> {
              crescendoUserServiceImpl.getByUserEmail(email);
            });

    assertEquals(
        "This User is Inactive on Crescendo, please activate to Proceed with sync",
        exception.getMessage());
  }

  @Test
  void getByUserEmail_QueryContainsCorrectFields() throws CustomDEExceptions {
    // Arrange
    String email = "<EMAIL>";
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            anyString(),
            any(ParameterizedTypeReference.class)))
        .thenReturn(mockResponse);

    // Act
    crescendoUserServiceImpl.getByUserEmail(email);

    // Assert - Verify the query contains all expected fields
    verify(crescendoApiServiceImpl)
        .getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            argThat(
                query ->
                    query.contains(
                            "SELECT+Id,Name,Email,Username,UserType,FirstName,LastName,LastLoginDate,Profile.UserLicense.Id,IsActive,Profile.UserLicense.Name+FROM+User")
                        && query.contains("WHERE+Email='" + email + "'")),
            any(ParameterizedTypeReference.class));
  }

  @Test
  void getByUserEmail_WithSpecialCharactersInEmail() throws CustomDEExceptions {
    // Arrange
    String email = "<EMAIL>";
    mockUser.setEmail(email);
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(mockResponse);

    // Act
    SalesforceRecordsResponse<Users> result = crescendoUserServiceImpl.getByUserEmail(email);

    // Assert
    assertNotNull(result);
    assertEquals(email, result.getRecords().get(0).getEmail());
  }

  @Test
  void getByUserEmail_NullIsActiveField() throws CustomDEExceptions {
    // Arrange
    String email = "<EMAIL>";
    Users userWithNullIsActive = new Users();
    userWithNullIsActive.setId("003XX000004TmiQQAS");
    userWithNullIsActive.setName("Test User");
    userWithNullIsActive.setEmail(email);
    userWithNullIsActive.setIsActive(null);

    SalesforceRecordsResponse<Users> responseWithNullIsActive = new SalesforceRecordsResponse<>();
    responseWithNullIsActive.setTotalSize(1);
    responseWithNullIsActive.setDone(true);
    responseWithNullIsActive.setRecords(Arrays.asList(userWithNullIsActive));

    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(mockTokenDto);
    when(crescendoApiServiceImpl.getRecordsQuery(
            eq(mockAuthToken),
            eq("/services/data/v58.0"),
            contains(email),
            any(ParameterizedTypeReference.class)))
        .thenReturn(responseWithNullIsActive);

    // Act - The implementation only checks for Boolean.FALSE.equals(), so null passes through
    SalesforceRecordsResponse<Users> result = crescendoUserServiceImpl.getByUserEmail(email);

    // Assert - Should return the response since null isActive is not treated as false
    assertNotNull(result);
    assertEquals(1, result.getTotalSize());
    assertNull(result.getRecords().get(0).getIsActive());
  }
}
