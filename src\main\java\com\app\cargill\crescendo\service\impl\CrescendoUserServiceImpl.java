/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class CrescendoUserServiceImpl implements ICrescendoUserService {

  private static final String SIMPLE_USERS_QUERY =
      "SELECT+Id,Name,Email,Username,UserType,FirstName,LastName,LastLoginDate,Profile.UserLicense.Id,IsActive,Profile.UserLicense.Name+FROM+User";

  private final CrescendoApiServiceImpl crescendoApiServiceImpl;

  @Override
  public SalesforceRecordsResponse<Users> getByUserEmail(String email) throws CustomDEExceptions {
    AccessTokenAndApiPathDto tokenDto = crescendoApiServiceImpl.getTokenAndApiPath();

    String query = SIMPLE_USERS_QUERY + "+WHERE+Email='" + email + "'";

    SalesforceRecordsResponse<Users> crescendoUser =
        crescendoApiServiceImpl.getRecordsQuery(
            tokenDto.getAuthToken(),
            tokenDto.getApiPath(),
            query,
            new ParameterizedTypeReference<>() {});

    if (Objects.isNull(crescendoUser) || Objects.isNull(crescendoUser.getRecords().get(0))) {
      log.error("User not found on Crescendo: {}", email);
      throw new CustomDEExceptions("User not found on Crescendo");

    } else if (crescendoUser.getTotalSize() > 1) {
      log.error("More than one user fetced against email: {}", email);
      throw new CustomDEExceptions("This email has more than one registration on Crecendo");
    } else if (Boolean.FALSE.equals(crescendoUser.getRecords().get(0).getIsActive())) {
      log.error("User inactive on Crescendo : {}", email);
      throw new CustomDEExceptions(
          "This User is Inactive on Crescendo, please activate to Proceed with sync");
    }

    return crescendoUser;
  }
}
