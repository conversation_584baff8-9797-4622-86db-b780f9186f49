/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import java.io.Serializable;
import java.util.List;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.app.cargill.crescendo.model.CrescendoMetadata;
import com.app.cargill.crescendo.service.ICrescendoApiService;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.api.model.VersionObject;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.LiftErrorResponse;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.service.LiftObjectDeletedException;
import com.app.cargill.sf.cc.utils.LiftErrorCode;
import com.app.cargill.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings({"java:S3077", "java:S2696"})
public class CrescendoApiServiceImpl implements ICrescendoApiService {

  private static final int HOUR = 1 * 60 * 60 * 1000;
  @Getter @Setter private static volatile CrescendoMetadata crescendoMetaData;
  @Getter private final CrescendoApiReactiveService crescendoApiReactiveService;

  @Override
  public AuthToken getAuthToken() {
    try {
      log.trace("Requesting CRESCENDO token");
      AuthToken authToken = crescendoApiReactiveService.getToken().block();
      log.info("CRESCENDO token obtained Successfully");
      log.info("Token Crescendo: {}", authToken.getAccessToken());
      return authToken;
    } catch (WebClientResponseException e) {
      log.error(e.getResponseBodyAsString());
      throw e;
    }
  }

  @Scheduled(fixedRate = HOUR)
  @EventListener(ApplicationReadyEvent.class)
  public CrescendoMetadata init() {
    AuthToken authToken = getAuthToken();
    String apiPath = getLatestApiPath(authToken);
    crescendoMetaData = CrescendoMetadata.builder().authToken(authToken).apiPath(apiPath).build();
    return crescendoMetaData;
  }

  public VersionObject getLatestApiVersion(AuthToken authToken) {
    return crescendoApiReactiveService.getLatestApiVersion(authToken).block();
  }

  public String getLatestApiPath(AuthToken authToken) {
    return getLatestApiVersion(authToken).getUrl();
  }

  public AccessTokenAndApiPathDto getTokenAndApiPath() {
    AuthToken authToken = null;
    String apiPath = null;
    if (getCrescendoMetaData() != null
        && getCrescendoMetaData().getAuthToken() != null
        && getCrescendoMetaData().getApiPath() != null) {
      authToken = getCrescendoMetaData().getAuthToken();
      apiPath = getCrescendoMetaData().getApiPath();
    }
    if (getCrescendoMetaData() == null || getCrescendoMetaData().getAuthToken().isExpired()) {
      authToken = getAuthToken();
      apiPath = getLatestApiPath(authToken);
      crescendoMetaData = CrescendoMetadata.builder().authToken(authToken).apiPath(apiPath).build();
    }

    return AccessTokenAndApiPathDto.builder().apiPath(apiPath).authToken(authToken).build();
  }

  public <T extends Serializable> SalesforceRecordsResponse<T> getRecordsQuery(
      AuthToken authToken,
      String apiPath,
      String query,
      ParameterizedTypeReference<SalesforceRecordsResponse<T>> typeReference) {
    log.trace("Making Crescendo query request: {}", query);
    SalesforceRecordsResponse<T> recordsResponse =
        crescendoApiReactiveService
            .getRecordsQuery(authToken, apiPath, query, typeReference)
            .block();
    log.trace(
        "Received {} records from Crescendo",
        recordsResponse != null ? recordsResponse.getTotalSize() : "NULL");
    return recordsResponse;
  }
  
  public <T extends Serializable> CreateRecordResponse createRecord(
	      AuthToken authToken, T obj, ParameterizedTypeReference<T> typeReference, String uri)
	      throws JsonProcessingException, CustomDEExceptions {
	    try {
	      CreateRecordResponse result =
	          crescendoApiReactiveService.createRecord(authToken, obj, typeReference, uri).block();
	      log.info("Result: {}", result);
	      return result;
	    } catch (Exception e) {
	      log.error(e.getMessage());
	      throw new CustomDEExceptions(e.getMessage());
	    }
	  }
  
  public <T extends Serializable> void updateRecord(
	      AuthToken authToken, T obj, ParameterizedTypeReference<T> typeReference, String uri) {
	    try {
	      crescendoApiReactiveService.updateRecord(authToken, obj, typeReference, uri).block();
	    } catch (WebClientResponseException e) {
	      ObjectMapper objectMapper = new ObjectMapper();
	      try {
	        List<LiftErrorResponse> liftErrorResponse =
	            objectMapper.readValue(e.getResponseBodyAsString(), new TypeReference<>() {});
	        if (liftErrorResponse.get(0).getErrorCode().equals(LiftErrorCode.ENTITY_IS_DELETED)) {
	          throw new LiftObjectDeletedException(uri);
	        }
	        log.error("CRESCENDO_UPDATE_ERROR_RESPONSE {}", e.getResponseBodyAsString());
	        log.error(
	            "CRESCENDO_UPDATE_ERROR_RESPONSE_DEBUG {} {} {}",
	            e.getResponseBodyAsString(),
	            typeReference,
	            obj);
	      } catch (JsonProcessingException ex) {
	        log.warn(
	            "CANNOT_DESERIALIZE_CRESCENDO_ERROR_RESPONSE {} {}",
	            ex.getMessage(),
	            e.getResponseBodyAsString());
	      }
	      throw e;
	    }
	  }
}
